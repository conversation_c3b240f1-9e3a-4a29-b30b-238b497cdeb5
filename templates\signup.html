{% extends "layout.html" %}

{% block body %}
    <div class="auth-container">
    <h2>Create Account</h2>
    
    <form class="auth-form" method="POST" action="{{ url_for('signup') }}">
      <input type="text" name="username" placeholder="Username" required />
      <input type="email" name="email" placeholder="Email" required />
      <input type="password" name="password" placeholder="Password" required />
      <input type="password" name="confirm_password" placeholder="Confirm Password" required />
      <button type="submit">Sign Up</button>
    </form>
    <p class="auth-footer">Already have an account? <a href="{{ url_for('signin')}}">Sign In</a></p>
    {% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    <div class="flash-messages">
      {% for category, message in messages %}
        <div class="flash-message {{ category }}">{{ message }}</div>
      {% endfor %}
    </div>
  {% endif %}
{% endwith %}
  </div>
  
{% endblock body %}