from flask import Flask, render_template, request, redirect, url_for, flash, session
import os
from models import db, Users, Admin
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash

app = Flask(__name__)
app.secret_key = 'she_shop_secret_key_2025'
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SECURE'] = False  # Set True if HTTPS
app.config["SQLALCHEMY_DATABASE_URI"] = 'sqlite:///she.db'
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False


db.init_app(app)

with app.app_context():
    admin_email = "adminshes@.com"
    admin_password = generate_password_hash("admin123")
    
    # Check if admin already exists
    existing_admin = Users.query.filter_by(email=admin_email).first()
    
    if not existing_admin:
        admin_user = Users(
            username="Admin",
            email=admin_email,
            address="Admin Address",
            phonenumber="01025489484",
            password=admin_password
        )
        db.session.add(admin_user)
        db.session.commit()


@app.route("/")
def home():
    print("SESSION >>>", dict(session))
    is_admin = session.get("is_admin", False)
    return render_template("home.html", is_admin=is_admin)

@app.route("/cart")
def cart():
    return render_template("cart.html")

@app.route("/checkout")
def checkout():
    return render_template("checkout.html")

@app.route("/signup", methods=["GET", "POST"])
def signup():
    if request.method == "POST":
        username = request.form.get("username")
        email = request.form.get("email")
        address = request.form.get("address")
        phonenumber = request.form.get("phonenumber")
        password = request.form.get("password")

        hashed_password = generate_password_hash(password)
        user = Users(
            username=username,
            email=email,
            address=address,
            phonenumber=phonenumber,
            password=hashed_password
        )
        db.session.add(user)
        db.session.commit()

        session["user_uid"] = user.uid
        session["email"] = user.email
        session["username"] = user.username
        session["is_admin"] = False


        flash("Account created successfully!", "success")
        return redirect(url_for("home"))


    return render_template("signup.html")


@app.route("/signin", methods=["GET", "POST"])
def signin():
    if request.method == "POST":
        email = request.form.get("email")
        password = request.form.get("password")

        # Check if admin login using app config
        if email == app.config["ADMIN_EMAIL"] and password == app.config["ADMIN_PASSWORD"]:
            admin = Admin.query.filter_by(email=email).first()
            session["user_uid"] = 0  # You can use 0 or admin.aid
            session["admin_aid"] = admin.aid
            session["is_admin"] = True
            session["username"] = admin.username
            flash("Welcome Admin!", "success")
            return redirect(url_for("home"))

        #Check for normal user
        user = Users.query.filter_by(email=email).first()
        if user and check_password_hash(user.password, password):
            session["user_uid"] = user.uid
            session["is_admin"] = False
            session["username"] = user.username
            flash("Login successful!", "success")
            return redirect(url_for("home"))

        flash("Invalid email or password", "danger")
    return render_template("signin.html")



@app.route("/logout")
def logout():
    session.clear()
    flash("You have been logged out", "success")
    return redirect(url_for("home"))

@app.route("/profile")
def profile():
    if session.get("is_admin"):
        admin = Admin.query.get(session.get("admin_aid"))
        return render_template("profile.html", admin=admin)
    elif session.get("user_uid"):
        user = Users.query.get(session["user_uid"])
        return render_template("profile.html", user=user)
    else:
        return redirect(url_for("signin"))


@app.route("/admin")
def admin():
    if not session.get("is_admin"):
        flash("Unauthorized access", "danger")
        return redirect(url_for("signin"))
    admin = Admin.query.get(session["admin_aid"])
    return render_template("admin.html", admin=admin)


if __name__ == '__main__':
    app.run(debug=True)
