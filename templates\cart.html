{% extends "base.html" %}

{% block content %}

    <div class="cart-page">
      <h2>Your Cart </h2>

      <!-- If cart is empty, show this -->
      <div class="empty-cart" id="emptyCart">
        <p>Your cart is currently empty.</p>
        <a href="/" class="btn-primary">Continue Shopping</a>
      </div>

      <!-- If cart has items, show this -->
      <div class="cart-items" id="cartItems">
        <!-- Example item, backend will loop and populate -->
        <div class="cart-item">
          <img src="/static/images/PinkFloralDress.webp" alt="Product" />
          <div class="item-details">
            <h4>Pink Floral Dress</h4>
            <p>Price: 300 EGP</p>
            <form method="POST" action="/remove-from-cart">
              <input type="hidden" name="item_id" value="1" />
              <button class="remove-btn" type="submit">Remove</button>
            </form>
          </div>
        </div>
      </div>

      <!-- Checkout Section -->
      <div class="checkout-box">
        <div class="summary-line">
          <span>Subtotal:</span>
          <span>300 EGP</span> <!-- Replace with {{ subtotal }} -->
        </div>
        <div class="summary-line">
          <span>Shipping:</span>
          <span>50 EGP</span> <!-- Replace with {{ shipping }} -->
        </div>
        <hr />
        <div class="summary-line total">
          <span>Total:</span>
          <span>350 EGP</span> <!-- Replace with {{ total }} -->
        </div>
        <a href="/checkout" class="checkout-btn">Checkout</a>
      </div>
    </div>



{% endblock content %}
