/* ========= GLOBAL RESET & BASE STYLES ========= */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
}

html, body {
    height: 100%;
    /* Keep overflow-x: hidden; unless you specifically want horizontal scrolling */
    overflow-x: hidden;
}

body {
    background-color: #fff;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.6;
}

/* Enable smooth scroll for anchor links */
  html {
    scroll-behavior: smooth;    
  }

/* ========= MAIN CONTENT ========= */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* ========= NAVBAR ========= */
.navbar {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(255, 105, 180, 0.2);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 16px 0;
}

.navbar-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 32px; /* Keep this padding for inner spacing */
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.logo-circle {
    background: linear-gradient(135deg, #ff85b3, #ff69b4);
    color: #fff;
    font-size: 25.6px;
    font-weight: 600;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(255, 105, 180, 0.3);
}

.navbar-search-form {
    display: flex;
    align-items: center;
    flex: 1;
    /* max-width: 500px; - Consider increasing this or removing if you want search to take more space */
    max-width: 700px; /* Increased to allow search bar more width */
    gap: 4px;
}

.navbar-search {
    background: #fff;
    border-radius: 50px;
    padding: 8px 16px;
    flex: 1;
    display: flex;
    align-items: center;
    border: 1px solid #ffc6dd;
    transition: 0.3s ease;
    min-width: 0; /* Prevents overflow on small screens */
    margin-top: 32px;
}

.navbar-search input {
    border: none;
    outline: none;
    font-size: 16px;
    flex: 1;
    background: transparent;
}

.search-btn {
    background: #ff69b4;
    border: none;
    color: white;
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 17.6px;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 32px;
}

.search-btn:hover {
    background: #ff4081;
}

.navbar-links {
    list-style: none;
    display: flex;
    gap: 24px;
    align-items: center;
}

.navbar-links li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;
    transition: background 0.3s, color 0.3s;
}

.navbar-links li a:hover {
    background-color: #ffe5f0;
    color: #ff4081;
}

.navbar-links li a .fa-shopping-cart {
    font-size: 19.2px;
    color: #333;
}

.navbar-links li a:hover .fa-shopping-cart {
    color: #ff4081;
}

/* ---------- Profile Page ----------- */
.profile-container {
  max-width: 700px; /* was 600px */
  margin: 80px auto;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(233, 30, 99, 0.1); /* soft pink shadow */
}

/* Heading */
.profile-heading {
  font-size: 40px;
  margin-bottom: 30px;
  color: #ff69b4;
  text-align: center;
  font-weight: 600;
}

/* Card Section */
.profile-card {
  background-color: #fdfdfd;
  padding: 35px 50px; /* increased padding for a bigger look */
  border-radius: 16px;
  border: 1px solid #eee;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Row for Each Detail */
.profile-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 22px 0; /* more vertical gap */
  padding-bottom: 14px;
  border-bottom: 1px solid #f0f0f0;
  gap: 32px; /* add horizontal gap between label and value */
}

/* Labels */
.profile-label {
  font-weight: bold;
  color: #555;
  font-size: 16px;
}

/* Values */
.profile-value {
  font-size: 16px;
  color: #333;
}

/* ========= HERO SECTION ========= */
.hero {
    background: linear-gradient(to right, #ffb6c1, #ff69b4);
    color: white;
    text-align: center;
    padding: 64px 0; /* Vertical padding only here */
    margin-top: 125px;
    width: 100%; /* Ensures background stretches full width */
}

/* Hero content needs a wider container or no max-width */
/* Assuming 'hero-content-container' wraps the text and button inside .hero */
.hero-content-container {
    max-width: 1400px; /* Increased max-width for hero content */
    margin: 0 auto;
    padding: 0 40px; /* More generous side padding */
}

.hero h1 {
    font-size: 40px;
    margin-bottom: 16px;
}

.hero p {
    font-size: 19.2px;
    margin-bottom: 32px;
}

.btn-primary {
    display: inline-block;
    background: #fff;
    color: #ff69b4;
    padding: 11.2px 24px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: 0.3s ease;
}

.btn-primary:hover {
    background: #ff69b4;
    color: #fff;
    box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3);
}

/* ========= CATEGORIES ========= */
.categories {
    padding: 48px 0;
    text-align: center;
    width: 100%; /* Ensures background stretches full width */
}

/* Categories content needs a wider container or no max-width */
/* Assuming 'categories-container' wraps the h2 and category-grid */
.categories-container {
    max-width: 1600px; /* Increased max-width for categories content */
    margin: 0 auto;
    padding: 0 40px; /* More generous side padding */
}

.categories h2 {
    font-size: 32px;
    color: #ff69b4;
    margin-bottom: 32px;
}

.category-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 32px;
}

.category-card {
    background: #fff0f5;
    border-radius: 12px;
    padding: 16px;
    width: 160px;
    box-shadow: 0 2px 10px rgba(255, 105, 180, 0.1);
    transition: 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(255, 105, 180, 0.2);
}

.category-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
}

.category-card h3 {
    margin-top: 11.2px;
    font-size: 16px;
    color: #ff69b4;
}

/* ========= PRODUCTS (New Arrivals in your image) ========= */
.featured-products {
    padding: 48px 0; /* Changed to vertical only padding here */
    text-align: center;
    width: 100%; /* Ensures background stretches full width */
}

/* Featured products content needs a wider container or no max-width */
/* Assuming 'featured-products-container' wraps the h2 and product-grid */
.featured-products-container {
    max-width: 1600px; /* Increased max-width for products content */
    margin: 0 auto;
    padding: 0 40px; /* More generous side padding */
}

.featured-products h2 {
    font-size: 32px;
    color: #ff69b4;
    margin-bottom: 32px;
}

.product-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    justify-content: center;
}

.product-card {
    background: #fff0f5;
    border-radius: 12px;
    padding: 16px;
    width: 200px;
    box-shadow: 0 2px 10px rgba(255, 105, 180, 0.1);
    transition: 0.3s ease;
}

.product-card:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.2);
}

.product-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 8px;
}

.product-card h3 {
    margin: 12.8px 0 4.8px;
    font-size: 17.6px;
    color: #ff69b4;
}

.product-card p {
    font-weight: bold;
    color: #333;
}

.btn-add {
    display: block;
    margin: 11.2px auto 0;
    background: #ff69b4;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    transition: 0.3s ease;
}

.btn-add:hover {
    background: #ff4081;
}

/* ========= ABOUT ========= */
.about-shop {
    padding-top: 32px;
    padding-bottom: 32px;
    text-align: center;
    background: #ffe4ec;
    width: 100%;
}

/* Ensure about-shop content fills wider as needed, or remove max-width here */
.about-shop p {
    max-width: 1000px; /* Increased for wider text */
    margin: 0 auto;
    font-size: 17.6px;
    color: #444;
    padding: 0 40px; /* More padding for smaller screens */
}

.about-shop h2 {
    font-size: 32px;
    color: #ff69b4;
    margin-bottom: 16px;
}


.home-about {
    margin-bottom: 32px;
}

/* ========= NEWSLETTER ========= */
.newsletter {
    padding: 32px 0;
    background: #fff0f5;
    text-align: center;
    width: 100%;
}

.newsletter-container {
    max-width: 1000px; /* Increased width for newsletter form */
    margin: 0 auto;
    padding: 0 40px; /* More padding for smaller screens */
}

.newsletter h2 {
    font-size: 28.8px;
    color: #ff69b4;
}

.newsletter p {
    margin: 8px 0 16px;
}

.newsletter input[type="email"] {
    padding: 9.6px;
    width: 300px; /* Slightly increased width for input */
    border-radius: 25px;
    border: 1px solid #ffb6c1;
    outline: none;
}

.newsletter button {
    padding: 9.6px 19.2px;
    border: none;
    background: #ff69b4;
    color: white;
    border-radius: 25px;
    margin-left: 8px;
    margin-right: 8px;
    cursor: pointer;
    transition: 0.3s;
}

.newsletter button:hover {
    background: #ff4081;
}

/* ========= AUTH FORMS - No changes needed for overall width here, as it's a specific form layout ========= */
.auth-container {
    max-width: 1200px;
    min-height: 80vh;
    margin: 80px auto;
    padding: 48px 32px;
    background-color: #fff0f5;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(255, 105, 180, 0.2);
    text-align: center;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 182, 193, 0.3);
}

.auth-container h2 {
    color: #ff69b4;
    margin-bottom: 32px;
    font-weight: 500;
    font-size: 35.2px;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    max-width: 600px;
    margin: 0 auto;
}

.auth-form input {
    padding: 16px 20px;
    border: 1px solid #ffcce0;
    border-radius: 30px;
    outline: none;
    font-size: 17.6px;
    transition: all 0.3s;
}

.auth-form button {
    padding: 16px;
    background-color: #ff69b4;
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 17.6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 16px;
}

.auth-form button:hover {
    transform: translateY(-2px);
    background-color: #ff4081;
    box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3);
}

.auth-footer {
    margin-top: 32px;
    font-size: 16px;
}

/* ========= FOOTER ========= */
.footer {
    background: #fff0f5;
    padding: 32px 0;
    text-align: center;
    border-top: 2px solid #ffc6dd;
    margin-top: 52px;
    width: 100%;
}

.footer-container {
    max-width: 1600px; /* Increased max-width for footer content */
    margin: 0 auto;
    padding: 0 40px; /* More generous side padding */
}

.footer-logo {
    font-size: 28.8px;
    font-weight: 600;
    color: #ff69b4;
    margin-bottom: 16px;
}

.footer-links {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
    margin: 16px 0;
    padding: 0;
}

.footer-links li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.footer-links li a:hover {
    color: #ff69b4;
}

.footer-socials {
    margin: 16px 0;
}

.footer-socials a {
    color: #ff69b4;
    margin: 0 9.6px;
    font-size: 19.2px;
    transition: color 0.3s ease;
}

.footer-socials a:hover {
    color: #ff4081;
}

.footer-copy {
    font-size: 14.4px;
    color: #777;
    margin-top: 16px;
}

/* --- General Cart Page Container --- */
.cart-page {
  max-width: 800px;
  margin: 48px auto; /* Equal top and bottom margin */
  margin-top: 176px;
  padding: 32px;
  background-color: #fff0f5;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(255, 105, 180, 0.1);
  font-family: 'Poppins', sans-serif;
  min-height: 60vh;
}

/* --- Heading --- */
.cart-page h2 {
  text-align: center;
  color: #ff69b4;
  margin-bottom: 32px;
}

/* --- Empty Cart Message --- */
.empty-cart {
  text-align: center;
  color: #777;
  font-size: 17.6px;
  background: #ffe4ec;
  padding: 32px;
  border-radius: 10px;
  display: none; /* Shown only when cart is empty */
}

.empty-cart .btn-primary {
  margin-top: 16px;
  text-decoration: none;
  background-color: #ff69b4;
  color: white;
  padding: 9.6px 19.2px;
  border-radius: 25px;
  display: inline-block;
  transition: background 0.3s ease;
}

.empty-cart .btn-primary:hover {
  background-color: #ff4081;
}

/* --- Cart Items Section --- */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* --- Single Cart Item Box --- */
.cart-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(255, 105, 180, 0.08);
  transition: 0.3s ease;
}

.cart-item img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 16px;
}

.item-details h4 {
  margin: 0;
  color: #ff69b4;
  font-size: 17.6px;
}

.item-details p {
  margin: 8px 0;
  font-size: 16px;
  color: #333;
}

.remove-btn {
  margin-top: 8px;
  background-color: #ff69b4;
  border: none;
  color: white;
  padding: 6.4px 14.4px;
  border-radius: 20px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.remove-btn:hover {
  background-color: #ff4081;
}

/* --- Checkout Summary Box --- */
.checkout-box {
  margin-top: 40px;
  text-align: right;
  background: #fff;
  padding: 24px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(255, 105, 180, 0.1);
  display: block;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  padding: 6.4px 0;
  font-size: 16px;
  color: #444;
}

.summary-line.total {
  font-weight: 600;
  font-size: 18.4px;
  margin-top: 16px;
}
.checkout-btn {
  display: block;               /* Makes it take up full width available */
  margin: 32px auto 0;          /* Top margin + auto left/right centers it */
  background-color: #ff69b4;
  color: white;
  padding: 9.6px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: background 0.3s ease;
  width: fit-content;           /* Optional: keep button size just right */
}


.checkout-btn:hover {
  background-color: #ff4081;
}

/* checkout page */
.checkout-container {
  max-width: 1000px;
  margin: 128px auto 64px; /* Adds space between navbar and box */
  padding: 32px;
  background-color: #fff0f5;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(255, 105, 180, 0.1);
  font-family: 'Poppins', sans-serif;
}

.checkout-container h2 {
  text-align: center;
  color: #ff69b4;
  margin-bottom: 32px;
}

/* Two column layout */
.checkout-sections {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: space-between;
}

.checkout-form, .order-summary {
  flex: 1 1 48%;
  background: #fff;
  padding: 24px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(255, 105, 180, 0.08);
}

/* Stack form fields vertically */
.checkout-form form {
  display: flex;
  flex-direction: column;
}

.checkout-form h3, .order-summary h3 {
  color: #ff69b4;
  margin-bottom: 16px;
}

.checkout-form form input,
.checkout-form form textarea {
  width: 100%;
  padding: 12.8px;
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 16px;
}

/* Order Summary details */
.summary-item, .summary-total {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
  font-size: 16px;
}

.summary-total {
  font-size: 19.2px;
  font-weight: bold;
}

/* Checkout Button */
.checkout-btn {
  display: block;
  margin: 32px auto 0;
  background-color: #ff69b4;
  color: white;
  padding: 11.2px 32px;
  border-radius: 30px;
  border: none;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.checkout-btn:hover {
  background-color: #ff4081;
}


/* Admin Dashboard  */
    :root {
      --primary: #f06292;
      --primary-dark: #e91e63;
      --bg-light: #fff0f6;
      --bg-white: #ffffff;
      --text-dark: #333;
      --text-light: #777;
      --border: #ffc6dd;
    }

    * {
      box-sizing: border-box;
      font-family: 'Poppins', sans-serif;
      margin: 0;
      padding: 0;
    }

    body {
      background: var(--bg-white);
      color: var(--text-dark);
      padding: 32px;
    }

    .dashboard-title {
      font-size: 32px;
      margin-bottom: 32px;
      color: var(--primary-dark);
      text-align: center;
    }

    .dashboard-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      justify-content: center;
    }

    .dashboard-card {
      background: var(--bg-light);
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      cursor: pointer;
      width: 250px;
      text-align: center;
      transition: 0.3s;
    }

    .dashboard-card:hover {
      background: #ffe0ec;
      transform: translateY(-3px);
    }

    .dashboard-card h3 {
      margin-bottom: 8px;
      color: var(--primary);
    }

    .dashboard-section {
      display: none;
      margin-top: 32px;
      background: var(--bg-light);
      padding: 32px;
      border-radius: 12px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    }

    .dashboard-section.active {
      display: block;
    }

    form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 32px;
    }

    form input {
      flex: 1;
      padding: 11.2px;
      border-radius: 8px;
      border: 1px solid var(--border);
    }

    form button {
      padding: 11.2px 24px;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    table th, table td {
      padding: 12.8px;
      border: 1px solid var(--border);
      text-align: left;
    }

    .logout-btn {
      display: inline-block;
      margin-top: 32px;
      text-decoration: none;
      color: white;
      background: var(--primary);
      padding: 9.6px 19.2px;
      border-radius: 25px;
    }


/* flash message */
.flash-messages {
  margin-bottom: 1rem;
}

.flash-message {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
}

.flash-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.flash-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Offset anchor scroll position for fixed navbar */
#new-arrivals {
  scroll-margin-top: 140px; /* Adjust to match your navbar + hero margin */
}