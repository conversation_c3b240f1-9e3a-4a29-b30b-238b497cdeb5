<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{% block title %}SHE Shop{% endblock %}</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <!-- Google Font -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet" />
  <script src="/static/script.js"></script> <!-- if you have JS -->

</head>
<body>

  <div class="main-content">
    <!-- Navbar -->
    <nav class="navbar">
      <div class="navbar-container">
        <!-- Logo -->
        <div class="navbar-logo logo-circle">She</div>
        <!-- Search Form -->
        <form class="navbar-search-form" action="/search" method="get">
          <div class="navbar-search">
            <input type="text" name="q" placeholder="Search..." />
          </div>
          <button type="submit" class="search-btn"><i class="fas fa-search"></i></button>
        </form>
        <!-- Navigation Links -->
      <ul class="navbar-links">
        <li><a href="{{ url_for('home') }}">Home</a></li>
        <li><a href="#about">About</a></li>
      {% if session.get('user_uid') is not none or session.get('admin_aid') is not none %}
        {% if session.get('is_admin') %}
          <!-- Admin -->
          <li><a href="{{ url_for('admin') }}">Dashboard</a></li>
          <li><a href="{{ url_for('logout') }}">Log Out</a></li>
        {% else %}
          <!-- Normal user -->
          <li><a href="{{ url_for('cart') }}"><i class="fas fa-shopping-cart"></i> Cart</a></li>  
          <li><a href="{{ url_for('logout') }}">Log Out</a></li>
          <li><a href="{{ url_for('profile') }}"><i class="fas fa-user-circle"></i></a></li>
        {% endif %}
      {% else %}
        <!-- Guest -->
        <li><a href="{{ url_for('signin') }}">Sign In</a></li>
        <li><a href="{{ url_for('signup') }}">Sign Up</a></li>
      {% endif %}


      </ul>

      </div>
    </nav>
  </div>

      {% block content %}{% endblock %}


      
  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-logo">SHE Shop</div>
  
      <ul class="footer-links">
        <li><a href="/about">About</a></li>
        <li><a href="/contact">Contact</a></li>
        <li><a href="/faq">FAQ</a></li>
        <li><a href="/privacy">Privacy</a></li>
      </ul>
  
      <div class="footer-socials">
        <a href="#"><i class="fab fa-facebook-f"></i></a>
        <a href="#"><i class="fab fa-instagram"></i></a>
        <a href="#"><i class="fab fa-twitter"></i></a>
      </div>
      
      <p class="footer-copy">&copy; 2025 SHE Shop. All rights reserved.</p>
    </div>
  </footer>

</body>
</html>
